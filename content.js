class ElementHider {
  constructor() {
    this.isHiding = false;
    this.hiddenElements = new Set();
    this.originalDisplayValues = new Map();
    this.highlightedElement = null;
    this.deleteButton = null;
    this.toolbar = null;

    // New advanced features
    this.persistentSelectors = new Set(); // CSS selectors for persistent hiding
    this.popupObserver = null; // MutationObserver for popup prevention
    this.autoCleanupEnabled = false;
    this.selectorMode = false;

    // Common ad/popup selectors for auto-cleanup
    this.commonAdSelectors = [
      '.ad', '.ads', '#ad', '#ads',
      '.advertisement', '.banner', '.popup',
      '.modal-overlay', '.cookie-banner',
      '[class*="ad-"]', '[id*="ad-"]',
      '[class*="popup"]', '[id*="popup"]',
      '.newsletter-popup', '.promo-popup'
    ];

    // Bind the event handlers to this instance
    this.handleMouseOver = this.handleMouseOver.bind(this);
    this.handleMouseOut = this.handleMouseOut.bind(this);
    this.handleHighlightedElementClick = this.handleHighlightedElementClick.bind(this);

    this.init();
  }
  
  init() {
    console.log('Element Hider Pro content script loaded');

    // Load persistent settings and apply hidden elements
    this.loadSettings().then(() => {
      this.applyPersistentHiding();
      this.setupPopupPrevention();
    });

    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      console.log('Received message:', request);

      switch(request.action) {
        case 'startHiding':
          this.startHiding();
          sendResponse({success: true});
          break;
        case 'stopHiding':
          this.stopHiding();
          sendResponse({success: true});
          break;
        case 'restoreAll':
          this.restoreAll();
          sendResponse({success: true});
          break;
        case 'getMode':
          sendResponse({mode: this.isHiding ? 'hiding' : 'normal'});
          break;
        case 'toggleAutoCleanup':
          this.toggleAutoCleanup();
          sendResponse({success: true, enabled: this.autoCleanupEnabled});
          break;
        case 'toggleSelectorMode':
          this.toggleSelectorMode();
          sendResponse({success: true, enabled: this.selectorMode});
          break;
        case 'hideBySelector':
          this.hideBySelector(request.selector);
          sendResponse({success: true});
          break;
        case 'exportSettings':
          this.exportSettings().then(data => sendResponse({success: true, data}));
          return true; // Keep channel open for async response
        case 'importSettings':
          this.importSettings(request.data).then(() => sendResponse({success: true}));
          return true; // Keep channel open for async response
      }
      return true; // Keep message channel open
    });
  }
  
  startHiding() {
    this.isHiding = true;
    document.body.style.cursor = 'crosshair';
    this.showToast('🎯 Hover over elements, then click the red area to hide them');
    this.createToolbar();
    
    // Add event listeners
    document.addEventListener('mouseover', this.handleMouseOver);
    document.addEventListener('mouseout', this.handleMouseOut);
  }
  
  stopHiding() {
    this.isHiding = false;
    document.body.style.cursor = '';
    this.clearHighlight();
    this.removeToolbar();
    this.showToast('✅ Hiding mode disabled');
    
    // Remove event listeners
    document.removeEventListener('mouseover', this.handleMouseOver);
    document.removeEventListener('mouseout', this.handleMouseOut);
  }
  
  createToolbar() {
    if (this.toolbar) return;
    
    this.toolbar = document.createElement('div');
    this.toolbar.className = 'element-hider-toolbar';
    
    // Create the toolbar content with proper event handling
    this.toolbar.innerHTML = `
      <span>🎯 Element Hider Pro Active</span>
    `;
    
    // Create the Done button separately so we can add event listener
    const doneButton = document.createElement('button');
    doneButton.textContent = 'Done';
    doneButton.addEventListener('click', () => {
      this.stopHiding();
    });
    
    this.toolbar.appendChild(doneButton);
    document.body.appendChild(this.toolbar);
  }
  
  removeToolbar() {
    if (this.toolbar) {
      this.toolbar.remove();
      this.toolbar = null;
    }
  }
  
  handleMouseOver = (e) => {
    if (!this.isHiding) return;
    
    const element = e.target;
    if (this.shouldIgnoreElement(element)) return;
    
    // Make sure we're passing the event object
    this.highlightElement(element, e);
  }
  
  handleMouseOut = (e) => {
    if (!this.isHiding) return;
    
    // Don't clear highlight if moving to delete button
    if (e.relatedTarget && e.relatedTarget.classList.contains('element-hider-delete-btn')) {
      return;
    }
    
    this.clearHighlight();
  }
  
  shouldIgnoreElement(element) {
    return element.tagName === 'HTML' || 
           element.tagName === 'BODY' ||
           element.classList.contains('element-hider-toast') ||
           element.classList.contains('element-hider-particle') ||
           element.classList.contains('element-hider-ripple') ||
           element.classList.contains('element-hider-toolbar') ||
           element.classList.contains('element-hider-delete-btn') ||
           element.closest('.element-hider-toolbar') ||
           element.closest('.element-hider-delete-btn');
  }
  
  highlightElement(element, mouseEvent) {
    this.clearHighlight();
    this.highlightedElement = element;
    element.classList.add('element-hider-highlight');

    // Add click handler to the highlighted element with capture=true for higher priority
    element.addEventListener('click', this.handleHighlightedElementClick, true);

    // Also add a backup click handler without capture
    element.addEventListener('click', this.handleHighlightedElementClick, false);

    // Log to debug
    console.log('Mouse position:', mouseEvent ? `${mouseEvent.clientX}, ${mouseEvent.clientY}` : 'No mouse event');
    console.log('Added click handlers to element:', element.tagName, element.className);

    // Create delete button at mouse position if available
    if (mouseEvent) {
      this.createDeleteButton(element, mouseEvent.clientX, mouseEvent.clientY);
    } else {
      // Fallback to center of element if no mouse event
      const rect = element.getBoundingClientRect();
      this.createDeleteButton(element, rect.left + rect.width / 2, rect.top + rect.height / 2);
    }
  }

  handleHighlightedElementClick = (e) => {
    if (!this.isHiding) return;

    // Prevent any default behavior and stop event propagation immediately
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();

    console.log('Highlighted element clicked! Hiding element:', this.highlightedElement?.tagName);

    // Hide the element that was clicked
    if (this.highlightedElement) {
      this.hideElementWithAnimation(this.highlightedElement, e.clientX, e.clientY);
    }
  }

  createDeleteButton(element, x, y) {
    if (this.deleteButton) {
      this.deleteButton.remove();
    }
    
    this.deleteButton = document.createElement('div');
    this.deleteButton.className = 'element-hider-delete-btn';
    this.deleteButton.innerHTML = 'Hide';
    this.deleteButton.title = 'Hide this element';
    
    // Position the button at the mouse cursor location
    this.deleteButton.style.position = 'fixed';
    this.deleteButton.style.left = x + 'px';
    this.deleteButton.style.top = y + 'px';
    
    // Add click handler
    this.deleteButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.hideElementWithAnimation(element, e.clientX, e.clientY);
    });
    
    // Prevent mouse events from bubbling
    this.deleteButton.addEventListener('mouseover', (e) => {
      e.stopPropagation();
    });
    
    this.deleteButton.addEventListener('mouseout', (e) => {
      e.stopPropagation();
      // Clear highlight when leaving delete button
      if (!element.contains(e.relatedTarget)) {
        this.clearHighlight();
      }
    });
    
    document.body.appendChild(this.deleteButton);
  }
  
  clearHighlight() {
    if (this.highlightedElement) {
      // Remove both click event listeners (capture and non-capture)
      this.highlightedElement.removeEventListener('click', this.handleHighlightedElementClick, true);
      this.highlightedElement.removeEventListener('click', this.handleHighlightedElementClick, false);
      this.highlightedElement.classList.remove('element-hider-highlight');
      this.highlightedElement = null;
    }

    if (this.deleteButton) {
      this.deleteButton.remove();
      this.deleteButton = null;
    }
  }
  
  hideElementWithAnimation(element, clickX, clickY) {
    // Store original display value
    const computedStyle = window.getComputedStyle(element);
    this.originalDisplayValues.set(element, computedStyle.display);

    // Generate CSS selector for persistent hiding
    const selector = this.generateSelector(element);
    if (selector) {
      this.persistentSelectors.add(selector);
      this.saveSettings(); // Save to storage
    }

    // Create ripple effect
    this.createRipple(clickX, clickY);

    // Create particles
    this.createParticles(element, clickX, clickY);

    // Hide element after animation
    setTimeout(() => {
      element.classList.add('element-hider-hidden');
      this.hiddenElements.add(element);

      // Completely hide after fade animation
      setTimeout(() => {
        element.style.display = 'none';
      }, 500);
    }, 300);

    this.clearHighlight();
    this.showToast(`🫥 Element hidden (${this.hiddenElements.size} total)`);
  }
  
  createRipple(x, y) {
    const ripple = document.createElement('div');
    ripple.className = 'element-hider-ripple';
    ripple.style.left = (x - 100) + 'px';
    ripple.style.top = (y - 100) + 'px';
    
    document.body.appendChild(ripple);
    
    setTimeout(() => {
      ripple.remove();
    }, 600);
  }
  
  createParticles(element, clickX, clickY) {
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    // Create 12 particles
    for (let i = 0; i < 12; i++) {
      const particle = document.createElement('div');
      particle.className = 'element-hider-particle';
      
      // Position at element center
      particle.style.left = centerX + 'px';
      particle.style.top = centerY + 'px';
      
      // Random direction and distance
      const angle = (i / 12) * Math.PI * 2;
      const distance = 100 + Math.random() * 100;
      const deltaX = Math.cos(angle) * distance;
      const deltaY = Math.sin(angle) * distance;
      
      particle.style.setProperty('--particle-x', deltaX + 'px');
      particle.style.setProperty('--particle-y', deltaY + 'px');
      
      // Random color variation
      const colors = ['#007AFF', '#FF3B30', '#34C759', '#FF9500', '#AF52DE'];
      particle.style.background = colors[Math.floor(Math.random() * colors.length)];
      
      document.body.appendChild(particle);
      
      setTimeout(() => {
        particle.remove();
      }, 1000);
    }
  }
  
  restoreAll() {
    this.hiddenElements.forEach(element => {
      element.classList.remove('element-hider-hidden');
      
      // Restore original display value
      const originalDisplay = this.originalDisplayValues.get(element);
      if (originalDisplay) {
        element.style.display = originalDisplay;
      } else {
        element.style.display = '';
      }
    });
    
    this.hiddenElements.clear();
    this.originalDisplayValues.clear();
    
    this.showToast('🔄 All elements restored!');
  }
  
  showToast(message) {
    // Remove existing toast
    const existingToast = document.querySelector('.element-hider-toast');
    if (existingToast) {
      existingToast.remove();
    }

    const toast = document.createElement('div');
    toast.className = 'element-hider-toast';
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
      toast.style.opacity = '0';
      setTimeout(() => {
        toast.remove();
      }, 300);
    }, 2000);
  }

  // === NEW ADVANCED FEATURES ===

  // Generate CSS selector for an element
  generateSelector(element) {
    try {
      // Try ID first
      if (element.id) {
        return `#${element.id}`;
      }

      // Try class combination
      if (element.className && typeof element.className === 'string') {
        const classes = element.className.trim().split(/\s+/)
          .filter(cls => cls && !cls.startsWith('element-hider-'))
          .slice(0, 3); // Limit to 3 classes for specificity
        if (classes.length > 0) {
          return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
        }
      }

      // Fallback to tag + nth-child
      const parent = element.parentElement;
      if (parent) {
        const siblings = Array.from(parent.children).filter(el => el.tagName === element.tagName);
        const index = siblings.indexOf(element) + 1;
        return `${element.tagName.toLowerCase()}:nth-of-type(${index})`;
      }

      return element.tagName.toLowerCase();
    } catch (error) {
      console.warn('Could not generate selector for element:', error);
      return null;
    }
  }

  // Load settings from Chrome storage
  async loadSettings() {
    try {
      const result = await chrome.storage.local.get(['persistentSelectors', 'autoCleanupEnabled']);
      this.persistentSelectors = new Set(result.persistentSelectors || []);
      this.autoCleanupEnabled = result.autoCleanupEnabled || false;
      console.log('Settings loaded:', {
        selectors: this.persistentSelectors.size,
        autoCleanup: this.autoCleanupEnabled
      });
    } catch (error) {
      console.warn('Could not load settings:', error);
    }
  }

  // Save settings to Chrome storage
  async saveSettings() {
    try {
      await chrome.storage.local.set({
        persistentSelectors: Array.from(this.persistentSelectors),
        autoCleanupEnabled: this.autoCleanupEnabled
      });
    } catch (error) {
      console.warn('Could not save settings:', error);
    }
  }

  // Apply persistent hiding on page load
  applyPersistentHiding() {
    const allSelectors = new Set([...this.persistentSelectors]);

    // Add auto-cleanup selectors if enabled
    if (this.autoCleanupEnabled) {
      this.commonAdSelectors.forEach(selector => allSelectors.add(selector));
    }

    allSelectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          if (!element.classList.contains('element-hider-hidden')) {
            element.style.display = 'none';
            element.classList.add('element-hider-hidden');
            this.hiddenElements.add(element);
          }
        });
      } catch (error) {
        console.warn('Invalid selector:', selector, error);
      }
    });

    if (this.hiddenElements.size > 0) {
      this.showToast(`🔄 ${this.hiddenElements.size} elements auto-hidden`);
    }
  }

  // Setup popup prevention using MutationObserver
  setupPopupPrevention() {
    if (this.popupObserver) {
      this.popupObserver.disconnect();
    }

    this.popupObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if this element matches any persistent selector
            this.persistentSelectors.forEach(selector => {
              try {
                if (node.matches && node.matches(selector)) {
                  console.log('Popup reappeared, hiding:', selector);
                  node.style.display = 'none';
                  node.classList.add('element-hider-hidden');
                  this.hiddenElements.add(node);
                }
                // Also check child elements
                const childMatches = node.querySelectorAll && node.querySelectorAll(selector);
                if (childMatches) {
                  childMatches.forEach(child => {
                    child.style.display = 'none';
                    child.classList.add('element-hider-hidden');
                    this.hiddenElements.add(child);
                  });
                }
              } catch (error) {
                console.warn('Error checking selector:', selector, error);
              }
            });
          }
        });
      });
    });

    this.popupObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  // Toggle auto-cleanup mode
  toggleAutoCleanup() {
    this.autoCleanupEnabled = !this.autoCleanupEnabled;
    this.saveSettings();

    if (this.autoCleanupEnabled) {
      this.applyPersistentHiding(); // Apply immediately
      this.showToast('🧼 Auto-cleanup enabled');
    } else {
      this.showToast('🧼 Auto-cleanup disabled');
    }
  }

  // Toggle selector mode
  toggleSelectorMode() {
    this.selectorMode = !this.selectorMode;
    this.showToast(this.selectorMode ? '🎯 Selector mode enabled' : '🎯 Selector mode disabled');
  }

  // Hide elements by CSS selector
  hideBySelector(selector) {
    try {
      const elements = document.querySelectorAll(selector);
      let hiddenCount = 0;

      elements.forEach(element => {
        if (!element.classList.contains('element-hider-hidden')) {
          element.style.display = 'none';
          element.classList.add('element-hider-hidden');
          this.hiddenElements.add(element);
          hiddenCount++;
        }
      });

      if (hiddenCount > 0) {
        this.persistentSelectors.add(selector);
        this.saveSettings();
        this.showToast(`🎯 Hidden ${hiddenCount} elements with selector: ${selector}`);
      } else {
        this.showToast('❌ No elements found for selector: ' + selector);
      }
    } catch (error) {
      this.showToast('❌ Invalid selector: ' + selector);
      console.warn('Invalid selector:', selector, error);
    }
  }

  // Export settings
  async exportSettings() {
    try {
      const settings = {
        persistentSelectors: Array.from(this.persistentSelectors),
        autoCleanupEnabled: this.autoCleanupEnabled,
        exportDate: new Date().toISOString(),
        version: '1.0'
      };
      return settings;
    } catch (error) {
      console.warn('Could not export settings:', error);
      throw error;
    }
  }

  // Import settings
  async importSettings(data) {
    try {
      if (data.persistentSelectors) {
        this.persistentSelectors = new Set(data.persistentSelectors);
      }
      if (typeof data.autoCleanupEnabled === 'boolean') {
        this.autoCleanupEnabled = data.autoCleanupEnabled;
      }

      await this.saveSettings();
      this.applyPersistentHiding();
      this.showToast('📥 Settings imported successfully');
    } catch (error) {
      console.warn('Could not import settings:', error);
      this.showToast('❌ Failed to import settings');
      throw error;
    }
  }
}

// Initialize the element hider
const elementHider = new ElementHider();











