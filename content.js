class ElementHider {
  constructor() {
    this.isHiding = false;
    this.hiddenElements = new Set();
    this.originalDisplayValues = new Map();
    this.highlightedElement = null;
    this.deleteButton = null;
    this.toolbar = null;

    // New advanced features
    this.persistentSelectors = new Set(); // CSS selectors for persistent hiding
    this.popupObserver = null; // MutationObserver for popup prevention
    this.autoCleanupEnabled = false;
    this.selectorMode = false;

    // Comprehensive ad/overlay selectors for manhwa sites
    this.commonAdSelectors = [
      // Google Ads and major ad networks
      '.adsbygoogle',
      'ins.adsbygoogle',
      '[data-ad-client]',
      'iframe[src*="googlesyndication"]',
      'iframe[src*="doubleclick"]',
      'iframe[src*="googletagmanager"]',

      // Alternative ad networks commonly used on manhwa sites
      'iframe[src*="pubrev.io"]',
      'iframe[src*="propellerads.com"]',
      'iframe[src*="popcash.net"]',
      'iframe[src*="popads.net"]',
      'iframe[src*="adnium.com"]',
      'iframe[src*="exoclick.com"]',
      'iframe[src*="trafficjunky.com"]',
      'iframe[src*="juicyads.com"]',
      'iframe[src*="adsterra.com"]',
      'iframe[src*="hilltopads.net"]',
      'iframe[src*="clickadu.com"]',
      'iframe[src*="bidvertiser.com"]',
      'iframe[src*="revcontent.com"]',
      'iframe[src*="outbrain.com"]',
      'iframe[src*="taboola.com"]',

      // Script-based ad networks
      'script[src*="pubrev.io"]',
      'script[src*="propellerads.com"]',
      'script[src*="popcash.net"]',
      'script[src*="popads.net"]',
      'script[src*="adnium.com"]',
      'script[src*="exoclick.com"]',
      'script[src*="monetix"]',
      'script[src*="adsterra.com"]',
      'script[src*="hilltopads.net"]',

      // Manhwa site specific ad patterns
      '[id*="banner"]',
      '[class*="banner"]',
      '[id*="ad-"]',
      '[class*="ad-"]',
      '[id*="_ad_"]',
      '[class*="_ad_"]',

      // Common ad dimensions (any element with these exact sizes)
      'div[style*="width: 300px"][style*="height: 250px"]',
      'div[style*="width: 728px"][style*="height: 90px"]',
      'div[style*="width: 160px"][style*="height: 600px"]',
      'div[style*="width: 320px"][style*="height: 50px"]',
      'div[style*="width: 970px"][style*="height: 250px"]',
      'div[style*="width: 336px"][style*="height: 280px"]',

      // Monetix and other ad networks common on manhwa sites
      '[class*="monetix"]',
      '[id*="monetix"]',
      '[data-monetix]',
      '[class*="adsystem"]',
      '[id*="adsystem"]',
      '[class*="propeller"]',
      '[id*="propeller"]',
      '[class*="exoclick"]',
      '[id*="exoclick"]',
      '[class*="adsterra"]',
      '[id*="adsterra"]',

      // Pop-under and redirect ad containers
      'div[onclick*="window.open"]',
      'div[onclick*="location.href"]',
      'a[href*="pubrev.io"]',
      'a[href*="propellerads.com"]',
      'a[href*="popcash.net"]',
      'a[href*="popads.net"]',

      // Overlay and popup patterns
      'div[style*="position: fixed"][style*="z-index"]',
      'div[style*="position: absolute"][style*="z-index: 999"]',
      'div[style*="position: absolute"][style*="z-index: 9999"]',
      '.overlay',
      '.popup',
      '.modal',
      '.backdrop',

      // Tailwind CSS modal/overlay patterns (common on manhwa sites)
      '.fixed.inset-0',
      'div[class*="fixed inset-0"]',
      'div[class*="bg-gray-900 bg-opacity"]',
      'div[class*="bg-black bg-opacity"]',
      'div[class*="z-50"]',
      'div[class*="z-40"]',
      'div[class*="z-30"]',
      '.fixed.z-50',
      '.fixed.z-40',
      '.fixed.z-30',

      // Newsletter and subscription overlays
      '[class*="newsletter"]',
      '[class*="subscribe"]',
      '[class*="signup"]',
      '[id*="newsletter"]',
      '[id*="subscribe"]',
      '[id*="signup"]',

      // Cookie and consent banners
      '[class*="cookie"]',
      '[class*="consent"]',
      '[class*="gdpr"]',
      '[id*="cookie"]',
      '[id*="consent"]',
      '[id*="gdpr"]',

      // Social media popups
      '[class*="social-popup"]',
      '[class*="share-popup"]',
      '[class*="follow-popup"]',

      // Promotional overlays
      '[class*="promo"]',
      '[class*="promotion"]',
      '[class*="discount"]',
      '[class*="sale"]',
      '[id*="promo"]',
      '[id*="promotion"]',

      // Specific ad containers
      '[id*="google_ads"]',
      '[class*="google-ads"]',
      '[id*="dl-banner"]',
      '[class*="advertisement"]',
      '[id*="advertisement"]',

      // Generic ad containers that might be dynamically created
      'div[id^="ad_"]',
      'div[class^="ad_"]',
      'div[id$="_ad"]',
      'div[class$="_ad"]'
    ];

    // Bind the event handlers to this instance
    this.handleMouseOver = this.handleMouseOver.bind(this);
    this.handleMouseOut = this.handleMouseOut.bind(this);
    this.handleHighlightedElementClick = this.handleHighlightedElementClick.bind(this);

    this.init();
  }
  
  init() {
    console.log('Element Hider Pro content script loaded');

    // Detect if this is a manhwa/manga site
    this.isManhwaSite = this.detectManhwaSite();
    if (this.isManhwaSite) {
      console.log('Manhwa site detected, using specialized mode');
    }

    // Load persistent settings and apply hidden elements
    this.loadSettings().then(() => {
      this.applyPersistentHiding();
      this.setupPopupPrevention();
      this.setupAggressiveOverlayDetection();
      this.setupRedirectBlocking();
    });

    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      console.log('Received message:', request);

      switch(request.action) {
        case 'startHiding':
          this.startHiding();
          sendResponse({success: true});
          break;
        case 'stopHiding':
          this.stopHiding();
          sendResponse({success: true});
          break;
        case 'restoreAll':
          this.restoreAll();
          sendResponse({success: true});
          break;
        case 'getMode':
          sendResponse({mode: this.isHiding ? 'hiding' : 'normal'});
          break;
        case 'toggleAutoCleanup':
          this.toggleAutoCleanup();
          sendResponse({success: true, enabled: this.autoCleanupEnabled});
          break;
        case 'toggleSelectorMode':
          this.toggleSelectorMode();
          sendResponse({success: true, enabled: this.selectorMode});
          break;
        case 'hideBySelector':
          this.hideBySelector(request.selector);
          sendResponse({success: true});
          break;
        case 'exportSettings':
          this.exportSettings().then(data => sendResponse({success: true, data}));
          return true; // Keep channel open for async response
        case 'importSettings':
          this.importSettings(request.data).then(() => sendResponse({success: true}));
          return true; // Keep channel open for async response
        case 'clearGenericSelectors':
          this.clearGenericSelectors();
          sendResponse({success: true});
          break;
        case 'scanOverlays':
          this.scanForOverlays();
          sendResponse({success: true});
          break;
        case 'removeTailwindOverlays':
          this.removeTailwindOverlays();
          sendResponse({success: true});
          break;
        case 'cleanManhwaAds':
          this.scanForAdsOutsideManhwa();
          sendResponse({success: true});
          break;
      }
      return true; // Keep message channel open
    });
  }
  
  startHiding() {
    this.isHiding = true;
    document.body.style.cursor = 'crosshair';
    this.showToast('🎯 Hover over elements, then click the red area to hide them');
    this.createToolbar();
    
    // Add event listeners
    document.addEventListener('mouseover', this.handleMouseOver);
    document.addEventListener('mouseout', this.handleMouseOut);
  }
  
  stopHiding() {
    this.isHiding = false;
    document.body.style.cursor = '';
    this.clearHighlight();
    this.removeToolbar();
    this.showToast('✅ Hiding mode disabled');
    
    // Remove event listeners
    document.removeEventListener('mouseover', this.handleMouseOver);
    document.removeEventListener('mouseout', this.handleMouseOut);
  }
  
  createToolbar() {
    if (this.toolbar) return;
    
    this.toolbar = document.createElement('div');
    this.toolbar.className = 'element-hider-toolbar';
    
    // Create the toolbar content with proper event handling
    this.toolbar.innerHTML = `
      <span>🎯 Element Hider Pro Active</span>
    `;
    
    // Create the Done button separately so we can add event listener
    const doneButton = document.createElement('button');
    doneButton.textContent = 'Done';
    doneButton.addEventListener('click', () => {
      this.stopHiding();
    });
    
    this.toolbar.appendChild(doneButton);
    document.body.appendChild(this.toolbar);
  }
  
  removeToolbar() {
    if (this.toolbar) {
      this.toolbar.remove();
      this.toolbar = null;
    }
  }
  
  handleMouseOver = (e) => {
    if (!this.isHiding) return;
    
    const element = e.target;
    if (this.shouldIgnoreElement(element)) return;
    
    // Make sure we're passing the event object
    this.highlightElement(element, e);
  }
  
  handleMouseOut = (e) => {
    if (!this.isHiding) return;
    
    // Don't clear highlight if moving to delete button
    if (e.relatedTarget && e.relatedTarget.classList.contains('element-hider-delete-btn')) {
      return;
    }
    
    this.clearHighlight();
  }
  
  shouldIgnoreElement(element) {
    return element.tagName === 'HTML' || 
           element.tagName === 'BODY' ||
           element.classList.contains('element-hider-toast') ||
           element.classList.contains('element-hider-particle') ||
           element.classList.contains('element-hider-ripple') ||
           element.classList.contains('element-hider-toolbar') ||
           element.classList.contains('element-hider-delete-btn') ||
           element.closest('.element-hider-toolbar') ||
           element.closest('.element-hider-delete-btn');
  }
  
  highlightElement(element, mouseEvent) {
    this.clearHighlight();
    this.highlightedElement = element;
    element.classList.add('element-hider-highlight');

    // Add click handler to the highlighted element with capture=true for higher priority
    element.addEventListener('click', this.handleHighlightedElementClick, true);

    // Also add a backup click handler without capture
    element.addEventListener('click', this.handleHighlightedElementClick, false);

    // Log to debug
    console.log('Mouse position:', mouseEvent ? `${mouseEvent.clientX}, ${mouseEvent.clientY}` : 'No mouse event');
    console.log('Added click handlers to element:', element.tagName, element.className);

    // Create delete button at mouse position if available
    if (mouseEvent) {
      this.createDeleteButton(element, mouseEvent.clientX, mouseEvent.clientY);
    } else {
      // Fallback to center of element if no mouse event
      const rect = element.getBoundingClientRect();
      this.createDeleteButton(element, rect.left + rect.width / 2, rect.top + rect.height / 2);
    }
  }

  handleHighlightedElementClick = (e) => {
    if (!this.isHiding) return;

    // Prevent any default behavior and stop event propagation immediately
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();

    console.log('Highlighted element clicked! Hiding element:', this.highlightedElement?.tagName);

    // Hide the element that was clicked
    if (this.highlightedElement) {
      this.hideElementWithAnimation(this.highlightedElement, e.clientX, e.clientY);
    }
  }

  createDeleteButton(element, x, y) {
    if (this.deleteButton) {
      this.deleteButton.remove();
    }
    
    this.deleteButton = document.createElement('div');
    this.deleteButton.className = 'element-hider-delete-btn';
    this.deleteButton.innerHTML = 'Hide';
    this.deleteButton.title = 'Hide this element';
    
    // Position the button at the mouse cursor location
    this.deleteButton.style.position = 'fixed';
    this.deleteButton.style.left = x + 'px';
    this.deleteButton.style.top = y + 'px';
    
    // Add click handler
    this.deleteButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.hideElementWithAnimation(element, e.clientX, e.clientY);
    });
    
    // Prevent mouse events from bubbling
    this.deleteButton.addEventListener('mouseover', (e) => {
      e.stopPropagation();
    });
    
    this.deleteButton.addEventListener('mouseout', (e) => {
      e.stopPropagation();
      // Clear highlight when leaving delete button
      if (!element.contains(e.relatedTarget)) {
        this.clearHighlight();
      }
    });
    
    document.body.appendChild(this.deleteButton);
  }
  
  clearHighlight() {
    if (this.highlightedElement) {
      // Remove both click event listeners (capture and non-capture)
      this.highlightedElement.removeEventListener('click', this.handleHighlightedElementClick, true);
      this.highlightedElement.removeEventListener('click', this.handleHighlightedElementClick, false);
      this.highlightedElement.classList.remove('element-hider-highlight');
      this.highlightedElement = null;
    }

    if (this.deleteButton) {
      this.deleteButton.remove();
      this.deleteButton = null;
    }
  }
  
  hideElementWithAnimation(element, clickX, clickY) {
    // Store original display value
    const computedStyle = window.getComputedStyle(element);
    this.originalDisplayValues.set(element, computedStyle.display);

    // Generate CSS selector for persistent hiding
    const selector = this.generateSelector(element);
    if (selector) {
      // Only save selectors that are specific enough (avoid generic ones)
      if (!this.isGenericSelector(selector)) {
        this.persistentSelectors.add(selector);
        this.saveSettings(); // Save to storage
        console.log('Saved persistent selector:', selector);
      } else {
        console.warn('Skipping generic selector:', selector);
      }
    }

    // Create ripple effect
    this.createRipple(clickX, clickY);

    // Create particles
    this.createParticles(element, clickX, clickY);

    // Hide element after animation
    setTimeout(() => {
      element.classList.add('element-hider-hidden');
      this.hiddenElements.add(element);

      // Completely hide after fade animation
      setTimeout(() => {
        element.style.display = 'none';
      }, 500);
    }, 300);

    this.clearHighlight();
    this.showToast(`🫥 Element hidden (${this.hiddenElements.size} total)`);
  }

  // Check if a selector is too generic and might cause issues
  isGenericSelector(selector) {
    // Avoid selectors that are just tag names
    if (/^(div|span|p|a|img|iframe|section|article|header|footer|main|nav)$/i.test(selector)) {
      return true;
    }

    // Avoid nth-of-type selectors (too generic)
    if (selector.includes(':nth-of-type') || selector.includes(':nth-child')) {
      return true;
    }

    // Avoid very short selectors without specific identifiers
    if (selector.length < 5) {
      return true;
    }

    // Avoid common CSS framework classes that are used for layout
    const commonFrameworkClasses = [
      // Tailwind CSS classes
      'flex', 'grid', 'block', 'inline', 'hidden', 'relative', 'absolute', 'fixed',
      'w-', 'h-', 'm-', 'p-', 'text-', 'bg-', 'border-', 'rounded-', 'shadow-',
      'justify-', 'items-', 'content-', 'self-', 'flex-col', 'flex-row',
      // Bootstrap classes
      'container', 'row', 'col-', 'd-', 'text-', 'bg-', 'border-', 'rounded-',
      'justify-content-', 'align-items-', 'flex-column', 'flex-row',
      // Common layout classes
      'wrapper', 'content', 'main', 'sidebar', 'header', 'footer', 'nav'
    ];

    // Check if selector contains only framework/layout classes
    const hasFrameworkClasses = commonFrameworkClasses.some(cls =>
      selector.includes(cls) || selector.includes(`.${cls}`)
    );

    if (hasFrameworkClasses && !this.hasSpecificAdIdentifiers(selector)) {
      console.warn('Blocking framework/layout selector:', selector);
      return true;
    }

    // Allow selectors with IDs (usually specific)
    if (selector.includes('#')) {
      return false;
    }

    // Allow selectors with specific attributes
    if (selector.includes('[') && (
      selector.includes('data-ad') ||
      selector.includes('data-google') ||
      selector.includes('id=') ||
      selector.includes('class=')
    )) {
      return false;
    }

    // Allow selectors with ad-specific classes
    if (this.hasSpecificAdIdentifiers(selector)) {
      return false;
    }

    // Default to considering it generic if we're unsure
    return true;
  }

  // Check if selector has specific ad-related identifiers (manhwa-site optimized)
  hasSpecificAdIdentifiers(selector) {
    const adKeywords = [
      'ad', 'ads', 'advertisement', 'banner', 'popup', 'modal', 'overlay',
      'google', 'adsense', 'doubleclick', 'amazon-ads', 'promo', 'newsletter',
      'cookie', 'gdpr', 'consent', 'subscribe', 'signup', 'monetix',
      // Alternative ad networks commonly used on manhwa sites
      'pubrev', 'propeller', 'popcash', 'popads', 'adnium', 'exoclick',
      'trafficjunky', 'juicyads', 'adsterra', 'hilltopads', 'clickadu',
      'bidvertiser', 'revcontent', 'outbrain', 'taboola',
      // Manhwa site specific ad patterns
      'dl-banner', 'ad-container', 'ad-wrapper', 'sponsored', 'promotion'
    ];

    // Also check for ad-like dimensions in the selector
    const adDimensionPatterns = [
      '300x250', '728x90', '160x600', '320x50', '970x250', '336x280',
      'width: 300px', 'height: 250px', 'width: 728px', 'height: 90px',
      'width: 160px', 'height: 600px', 'width: 320px', 'height: 50px'
    ];

    const lowerSelector = selector.toLowerCase();
    return adKeywords.some(keyword => lowerSelector.includes(keyword)) ||
           adDimensionPatterns.some(pattern => lowerSelector.includes(pattern));
  }
  
  createRipple(x, y) {
    const ripple = document.createElement('div');
    ripple.className = 'element-hider-ripple';
    ripple.style.left = (x - 100) + 'px';
    ripple.style.top = (y - 100) + 'px';
    
    document.body.appendChild(ripple);
    
    setTimeout(() => {
      ripple.remove();
    }, 600);
  }
  
  createParticles(element, clickX, clickY) {
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    // Create 12 particles
    for (let i = 0; i < 12; i++) {
      const particle = document.createElement('div');
      particle.className = 'element-hider-particle';
      
      // Position at element center
      particle.style.left = centerX + 'px';
      particle.style.top = centerY + 'px';
      
      // Random direction and distance
      const angle = (i / 12) * Math.PI * 2;
      const distance = 100 + Math.random() * 100;
      const deltaX = Math.cos(angle) * distance;
      const deltaY = Math.sin(angle) * distance;
      
      particle.style.setProperty('--particle-x', deltaX + 'px');
      particle.style.setProperty('--particle-y', deltaY + 'px');
      
      // Random color variation
      const colors = ['#007AFF', '#FF3B30', '#34C759', '#FF9500', '#AF52DE'];
      particle.style.background = colors[Math.floor(Math.random() * colors.length)];
      
      document.body.appendChild(particle);
      
      setTimeout(() => {
        particle.remove();
      }, 1000);
    }
  }
  
  restoreAll() {
    this.hiddenElements.forEach(element => {
      element.classList.remove('element-hider-hidden');
      
      // Restore original display value
      const originalDisplay = this.originalDisplayValues.get(element);
      if (originalDisplay) {
        element.style.display = originalDisplay;
      } else {
        element.style.display = '';
      }
    });
    
    this.hiddenElements.clear();
    this.originalDisplayValues.clear();
    
    this.showToast('🔄 All elements restored!');
  }
  
  showToast(message) {
    // Remove existing toast
    const existingToast = document.querySelector('.element-hider-toast');
    if (existingToast) {
      existingToast.remove();
    }

    const toast = document.createElement('div');
    toast.className = 'element-hider-toast';
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
      toast.style.opacity = '0';
      setTimeout(() => {
        toast.remove();
      }, 300);
    }, 2000);
  }

  // === NEW ADVANCED FEATURES ===

  // Generate CSS selector for an element
  generateSelector(element) {
    try {
      // Try ID first (most specific)
      if (element.id && element.id.trim()) {
        return `#${CSS.escape(element.id)}`;
      }

      // Try class combination (but be more specific)
      if (element.className && typeof element.className === 'string') {
        const classes = element.className.trim().split(/\s+/)
          .filter(cls => cls && !cls.startsWith('element-hider-'))
          .slice(0, 3); // Limit to 3 classes for specificity
        if (classes.length > 0) {
          const escapedClasses = classes.map(cls => CSS.escape(cls)).join('.');
          return `${element.tagName.toLowerCase()}.${escapedClasses}`;
        }
      }

      // Try data attributes (often unique)
      for (const attr of element.attributes) {
        if (attr.name.startsWith('data-') && attr.value) {
          return `${element.tagName.toLowerCase()}[${attr.name}="${CSS.escape(attr.value)}"]`;
        }
      }

      // Try other unique attributes
      const uniqueAttrs = ['role', 'aria-label', 'title'];
      for (const attrName of uniqueAttrs) {
        const attrValue = element.getAttribute(attrName);
        if (attrValue && attrValue.trim()) {
          return `${element.tagName.toLowerCase()}[${attrName}="${CSS.escape(attrValue)}"]`;
        }
      }

      // Build a more specific path-based selector
      const path = [];
      let current = element;
      let maxDepth = 3; // Limit depth to avoid overly long selectors

      while (current && current !== document.body && maxDepth > 0) {
        let selector = current.tagName.toLowerCase();

        // Add ID if available
        if (current.id) {
          selector += `#${CSS.escape(current.id)}`;
          path.unshift(selector);
          break; // ID is unique, we can stop here
        }

        // Add first class if available
        if (current.className && typeof current.className === 'string') {
          const firstClass = current.className.trim().split(/\s+/)[0];
          if (firstClass && !firstClass.startsWith('element-hider-')) {
            selector += `.${CSS.escape(firstClass)}`;
          }
        }

        path.unshift(selector);
        current = current.parentElement;
        maxDepth--;
      }

      if (path.length > 0) {
        return path.join(' > ');
      }

      // Last resort: don't use nth-of-type as it's too generic
      console.warn('Could not generate specific selector for element, skipping:', element);
      return null;
    } catch (error) {
      console.warn('Could not generate selector for element:', error);
      return null;
    }
  }

  // Load settings from Chrome storage
  async loadSettings() {
    try {
      const result = await chrome.storage.local.get(['persistentSelectors', 'autoCleanupEnabled']);
      const rawSelectors = result.persistentSelectors || [];

      // Filter out generic selectors from existing data
      const validSelectors = rawSelectors.filter(selector => !this.isGenericSelector(selector));
      this.persistentSelectors = new Set(validSelectors);

      // If we filtered out selectors, save the cleaned list
      if (validSelectors.length !== rawSelectors.length) {
        console.log(`Cleaned up ${rawSelectors.length - validSelectors.length} generic selectors`);
        this.saveSettings();
      }

      this.autoCleanupEnabled = result.autoCleanupEnabled || false;
      console.log('Settings loaded:', {
        selectors: this.persistentSelectors.size,
        autoCleanup: this.autoCleanupEnabled
      });
    } catch (error) {
      console.warn('Could not load settings:', error);
    }
  }

  // Save settings to Chrome storage
  async saveSettings() {
    try {
      await chrome.storage.local.set({
        persistentSelectors: Array.from(this.persistentSelectors),
        autoCleanupEnabled: this.autoCleanupEnabled
      });
    } catch (error) {
      console.warn('Could not save settings:', error);
    }
  }

  // Apply persistent hiding on page load
  applyPersistentHiding() {
    let hiddenCount = 0;

    // Apply user-defined persistent selectors (always apply these)
    this.persistentSelectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          if (!element.classList.contains('element-hider-hidden')) {
            element.style.display = 'none';
            element.classList.add('element-hider-hidden');
            this.hiddenElements.add(element);
            hiddenCount++;
          }
        });
      } catch (error) {
        console.warn('Invalid persistent selector:', selector, error);
      }
    });

    // Apply auto-cleanup selectors if enabled (with safety checks)
    if (this.autoCleanupEnabled) {
      this.commonAdSelectors.forEach(selector => {
        try {
          const elements = document.querySelectorAll(selector);
          elements.forEach(element => {
            // Safety checks to avoid hiding important content
            if (this.isSafeToHide(element) && !element.classList.contains('element-hider-hidden')) {
              element.style.display = 'none';
              element.classList.add('element-hider-hidden');
              this.hiddenElements.add(element);
              hiddenCount++;
            }
          });
        } catch (error) {
          console.warn('Invalid auto-cleanup selector:', selector, error);
        }
      });
    }

    if (hiddenCount > 0) {
      this.showToast(`🔄 ${hiddenCount} elements auto-hidden`);
    }
  }

  // Safety check to determine if an element is safe to hide (manhwa-site optimized)
  isSafeToHide(element) {
    // Don't hide if element contains substantial text content (likely main content)
    const textContent = element.textContent?.trim() || '';
    if (textContent.length > 500) {
      return false;
    }

    // MANHWA-SPECIFIC: Don't hide image containers that might contain manga pages
    if (element.tagName === 'IMG' || element.querySelector('img')) {
      const imgSrc = element.tagName === 'IMG' ? element.src : element.querySelector('img')?.src;
      if (imgSrc && (
        imgSrc.includes('storage/media') ||
        imgSrc.includes('chapter') ||
        imgSrc.includes('page') ||
        imgSrc.includes('manhwa') ||
        imgSrc.includes('manga') ||
        imgSrc.includes('comic')
      )) {
        console.log('Protecting manhwa content image:', imgSrc);
        return false;
      }
    }

    // Don't hide navigation elements
    if (element.tagName === 'NAV' ||
        element.closest('nav') ||
        element.classList.contains('navigation') ||
        element.classList.contains('menu') ||
        element.id?.includes('nav') ||
        element.id?.includes('menu')) {
      return false;
    }

    // Don't hide main content areas
    if (element.tagName === 'MAIN' ||
        element.tagName === 'ARTICLE' ||
        element.classList.contains('content') ||
        element.classList.contains('main') ||
        element.classList.contains('chapter') ||
        element.id === 'main' ||
        element.id === 'content' ||
        element.id?.includes('chapter')) {
      return false;
    }

    // MANHWA-SPECIFIC: Don't hide chapter navigation or reading controls
    const manhwaKeywords = /\b(chapter|prev|next|page|read|series|comic|manga|manhwa)\b/i;
    const classAndId = (element.className + ' ' + element.id).toLowerCase();
    if (manhwaKeywords.test(classAndId) && !classAndId.includes('ad')) {
      return false;
    }

    // Don't hide headers and footers unless they're clearly ads
    if ((element.tagName === 'HEADER' || element.tagName === 'FOOTER') &&
        !element.classList.toString().includes('ad') &&
        !element.id?.includes('ad')) {
      return false;
    }

    // Don't hide form elements
    if (element.tagName === 'FORM' || element.closest('form')) {
      return false;
    }

    // Check if element is positioned like a popup/overlay
    const style = window.getComputedStyle(element);
    const isOverlay = style.position === 'fixed' ||
                     style.position === 'absolute' ||
                     parseInt(style.zIndex) > 1000;

    // If it's an overlay with small content, it's likely a popup
    if (isOverlay && textContent.length < 200) {
      return true;
    }

    // Check for ad-like characteristics
    const hasAdKeywords = /\b(ad|ads|advertisement|banner|popup|promo|newsletter|subscribe|cookie|gdpr|monetix)\b/i;

    if (hasAdKeywords.test(classAndId)) {
      return true;
    }

    // MANHWA-SPECIFIC: Check for common ad dimensions
    const rect = element.getBoundingClientRect();
    const commonAdSizes = [
      {w: 300, h: 250}, // Medium Rectangle
      {w: 728, h: 90},  // Leaderboard
      {w: 160, h: 600}, // Wide Skyscraper
      {w: 320, h: 50},  // Mobile Banner
      {w: 970, h: 250}  // Large Rectangle
    ];

    const isAdSize = commonAdSizes.some(size =>
      Math.abs(rect.width - size.w) < 10 && Math.abs(rect.height - size.h) < 10
    );

    if (isAdSize && !manhwaKeywords.test(classAndId)) {
      console.log('Detected ad-sized element:', rect.width, 'x', rect.height);
      return true;
    }

    // Default to not hiding if we're unsure
    return false;
  }

  // Setup popup prevention using MutationObserver
  setupPopupPrevention() {
    if (this.popupObserver) {
      this.popupObserver.disconnect();
    }

    this.popupObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check against user-defined persistent selectors (always block these)
            this.persistentSelectors.forEach(selector => {
              try {
                if (node.matches && node.matches(selector)) {
                  console.log('Previously hidden element reappeared, blocking:', selector);
                  node.style.display = 'none';
                  node.classList.add('element-hider-hidden');
                  this.hiddenElements.add(node);
                }
                // Also check child elements
                const childMatches = node.querySelectorAll && node.querySelectorAll(selector);
                if (childMatches) {
                  childMatches.forEach(child => {
                    child.style.display = 'none';
                    child.classList.add('element-hider-hidden');
                    this.hiddenElements.add(child);
                  });
                }
              } catch (error) {
                console.warn('Error checking persistent selector:', selector, error);
              }
            });

            // Check against auto-cleanup selectors only if enabled and safe
            if (this.autoCleanupEnabled) {
              this.commonAdSelectors.forEach(selector => {
                try {
                  if (node.matches && node.matches(selector) && this.isSafeToHide(node)) {
                    console.log('Auto-cleanup blocking reappearing element:', selector);
                    node.style.display = 'none';
                    node.classList.add('element-hider-hidden');
                    this.hiddenElements.add(node);
                  }
                  // Check child elements with safety
                  const childMatches = node.querySelectorAll && node.querySelectorAll(selector);
                  if (childMatches) {
                    childMatches.forEach(child => {
                      if (this.isSafeToHide(child)) {
                        child.style.display = 'none';
                        child.classList.add('element-hider-hidden');
                        this.hiddenElements.add(child);
                      }
                    });
                  }
                } catch (error) {
                  console.warn('Error checking auto-cleanup selector:', selector, error);
                }
              });
            }
          }
        });
      });
    });

    this.popupObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  // Toggle auto-cleanup mode
  toggleAutoCleanup() {
    this.autoCleanupEnabled = !this.autoCleanupEnabled;
    this.saveSettings();

    if (this.autoCleanupEnabled) {
      this.applyPersistentHiding(); // Apply immediately
      this.showToast('🧼 Auto-cleanup enabled');
    } else {
      this.showToast('🧼 Auto-cleanup disabled');
    }
  }

  // Toggle selector mode
  toggleSelectorMode() {
    this.selectorMode = !this.selectorMode;
    this.showToast(this.selectorMode ? '🎯 Selector mode enabled' : '🎯 Selector mode disabled');
  }

  // Hide elements by CSS selector
  hideBySelector(selector) {
    try {
      const elements = document.querySelectorAll(selector);
      let hiddenCount = 0;
      let skippedCount = 0;

      elements.forEach(element => {
        if (!element.classList.contains('element-hider-hidden')) {
          // For user-defined selectors, we're more permissive but still warn about potentially important content
          const isImportantContent = !this.isSafeToHide(element);

          if (isImportantContent) {
            console.warn('Hiding potentially important content with selector:', selector, element);
            skippedCount++;
          }

          // Hide the element regardless (user explicitly requested it)
          element.style.display = 'none';
          element.classList.add('element-hider-hidden');
          this.hiddenElements.add(element);
          hiddenCount++;
        }
      });

      if (hiddenCount > 0) {
        this.persistentSelectors.add(selector);
        this.saveSettings();

        let message = `🎯 Hidden ${hiddenCount} elements with selector: ${selector}`;
        if (skippedCount > 0) {
          message += ` (${skippedCount} may be important content)`;
        }
        this.showToast(message);
      } else {
        this.showToast('❌ No elements found for selector: ' + selector);
      }
    } catch (error) {
      this.showToast('❌ Invalid selector: ' + selector);
      console.warn('Invalid selector:', selector, error);
    }
  }

  // Export settings
  async exportSettings() {
    try {
      const settings = {
        persistentSelectors: Array.from(this.persistentSelectors),
        autoCleanupEnabled: this.autoCleanupEnabled,
        exportDate: new Date().toISOString(),
        version: '1.0'
      };
      return settings;
    } catch (error) {
      console.warn('Could not export settings:', error);
      throw error;
    }
  }

  // Import settings
  async importSettings(data) {
    try {
      if (data.persistentSelectors) {
        this.persistentSelectors = new Set(data.persistentSelectors);
      }
      if (typeof data.autoCleanupEnabled === 'boolean') {
        this.autoCleanupEnabled = data.autoCleanupEnabled;
      }

      await this.saveSettings();
      this.applyPersistentHiding();
      this.showToast('📥 Settings imported successfully');
    } catch (error) {
      console.warn('Could not import settings:', error);
      this.showToast('❌ Failed to import settings');
      throw error;
    }
  }

  // Clear generic selectors that might be causing issues
  clearGenericSelectors() {
    const originalSize = this.persistentSelectors.size;
    const originalSelectors = Array.from(this.persistentSelectors);

    console.log('Checking selectors for removal:', originalSelectors);

    const validSelectors = originalSelectors.filter(selector => {
      const isGeneric = this.isGenericSelector(selector);
      if (isGeneric) {
        console.log('Removing generic selector:', selector);
      }
      return !isGeneric;
    });

    this.persistentSelectors = new Set(validSelectors);

    const removedCount = originalSize - this.persistentSelectors.size;
    if (removedCount > 0) {
      this.saveSettings();
      this.showToast(`🧹 Removed ${removedCount} problematic selectors`);
      console.log(`Cleared ${removedCount} problematic selectors:`,
        originalSelectors.filter(s => !validSelectors.includes(s)));

      // Also clear current hidden elements to reset the page
      this.hiddenElements.forEach(element => {
        element.style.display = '';
        element.classList.remove('element-hider-hidden');
      });
      this.hiddenElements.clear();
      this.originalDisplayValues.clear();

      this.showToast('🔄 Page reset - refresh to see changes');
    } else {
      this.showToast('✅ No problematic selectors found');
    }
  }

  // Detect if current site is a manhwa/manga reading site
  detectManhwaSite() {
    const hostname = window.location.hostname.toLowerCase();
    const manhwaSites = [
      'asuracomic.net', 'asurascans.com', 'mangadex.org', 'mangakakalot.com',
      'manganelo.com', 'mangapark.net', 'webtoons.com', 'tapas.io',
      'lezhin.com', 'toomics.com', 'mangaplus.shueisha.co.jp'
    ];

    // Check hostname
    if (manhwaSites.some(site => hostname.includes(site))) {
      return true;
    }

    // Check for manhwa-related keywords in URL or page content
    const url = window.location.href.toLowerCase();
    const manhwaKeywords = ['manga', 'manhwa', 'manhua', 'comic', 'chapter', 'webtoon'];

    if (manhwaKeywords.some(keyword => url.includes(keyword))) {
      return true;
    }

    // Check page title and meta tags
    const title = document.title.toLowerCase();
    const metaKeywords = document.querySelector('meta[name="keywords"]')?.content?.toLowerCase() || '';

    if (manhwaKeywords.some(keyword => title.includes(keyword) || metaKeywords.includes(keyword))) {
      return true;
    }

    return false;
  }

  // Aggressive overlay detection that runs continuously
  setupAggressiveOverlayDetection() {
    // Run initial scan
    this.scanForOverlays();

    // Set up periodic scanning for new overlays
    setInterval(() => {
      this.scanForOverlays();
    }, 2000); // Check every 2 seconds

    // Also scan when page becomes visible (in case ads load when user returns)
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        setTimeout(() => this.scanForOverlays(), 500);
      }
    });
  }

  // Scan for and hide overlay elements and ads
  scanForOverlays() {
    if (!this.autoCleanupEnabled && this.persistentSelectors.size === 0) {
      return; // Don't scan if no auto-cleanup and no persistent selectors
    }

    // Also scan for ads outside manhwa content
    this.scanForAdsOutsideManhwa();

    // Find all potentially problematic overlays
    const overlaySelectors = [
      // High z-index elements (likely overlays)
      'div[style*="z-index: 999"]',
      'div[style*="z-index: 9999"]',
      'div[style*="z-index: 99999"]',

      // Fixed position elements that cover the screen
      'div[style*="position: fixed"][style*="top: 0"]',
      'div[style*="position: fixed"][style*="left: 0"]',
      'div[style*="position: fixed"][style*="width: 100%"]',
      'div[style*="position: fixed"][style*="height: 100%"]',

      // Tailwind CSS overlay patterns (the specific class you mentioned)
      'div[class*="fixed inset-0"]',
      'div[class*="bg-gray-900 bg-opacity"]',
      'div[class*="bg-black bg-opacity"]',
      'div[class*="z-50"]',
      'div[class*="z-40"]',
      'div[class*="z-30"]',
      'div.fixed.inset-0',
      'div.fixed.z-50',
      'div.fixed.z-40',
      'div.fixed.z-30',

      // The exact problematic class pattern
      'div[class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 p-4 overflow-y-auto"]',
      'div[class*="fixed inset-0"][class*="bg-gray-900"][class*="bg-opacity"]',
      'div[class*="fixed inset-0"][class*="bg-black"][class*="bg-opacity"]',
      'div[class*="fixed inset-0"][class*="flex items-center justify-center"]',

      // Common overlay patterns
      'div[class*="overlay"]:not([class*="tooltip"])',
      'div[class*="modal"]:not([class*="tooltip"])',
      'div[class*="popup"]:not([class*="menu"])',
      'div[class*="backdrop"]',

      // Ad-specific overlays
      'div[id*="ad"][style*="position: fixed"]',
      'div[class*="ad"][style*="position: fixed"]',
      'iframe[style*="position: fixed"]'
    ];

    overlaySelectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          if (this.isLikelyAdOverlay(element) && !element.classList.contains('element-hider-hidden')) {
            console.log('Auto-hiding detected overlay:', element);
            element.style.display = 'none';
            element.classList.add('element-hider-hidden');
            this.hiddenElements.add(element);
          }
        });
      } catch (error) {
        console.warn('Error scanning overlay selector:', selector, error);
      }
    });
  }

  // Check if an element is likely an ad overlay
  isLikelyAdOverlay(element) {
    const style = window.getComputedStyle(element);
    const rect = element.getBoundingClientRect();
    const classAndId = (element.className + ' ' + element.id).toLowerCase();

    // Check for the specific problematic Tailwind class pattern
    const hasTailwindOverlayPattern = element.className.includes('fixed inset-0') ||
      (element.className.includes('fixed') &&
       element.className.includes('inset-0') &&
       (element.className.includes('bg-gray-900') || element.className.includes('bg-black')) &&
       element.className.includes('bg-opacity'));

    if (hasTailwindOverlayPattern) {
      console.log('Detected Tailwind overlay pattern:', element.className);
      // Make sure it's not manhwa content before hiding
      if (!this.isManhwaContent(element)) {
        return true;
      }
    }

    // Must be positioned as overlay
    if (style.position !== 'fixed' && style.position !== 'absolute') {
      return false;
    }

    // Check z-index (more lenient for Tailwind classes)
    const zIndex = parseInt(style.zIndex);
    if (isNaN(zIndex) || zIndex < 10) {
      return false;
    }

    // Check for ad-like characteristics
    const hasAdKeywords = /\b(ad|ads|banner|popup|modal|overlay|promo|newsletter|subscribe|monetix|pubrev|propeller)\b/i;

    if (hasAdKeywords.test(classAndId)) {
      return true;
    }

    // Check for Tailwind overlay classes specifically
    const tailwindOverlayKeywords = /\b(fixed|inset-0|bg-gray-900|bg-black|bg-opacity|z-50|z-40|z-30)\b/;
    if (tailwindOverlayKeywords.test(element.className)) {
      // If it has overlay classes and covers significant screen area
      const viewportArea = window.innerWidth * window.innerHeight;
      const elementArea = rect.width * rect.height;
      const coverageRatio = elementArea / viewportArea;

      if (coverageRatio > 0.5) { // Covers more than 50% of screen
        console.log('Large Tailwind overlay detected:', element.className, 'Coverage:', coverageRatio);
        if (!this.isManhwaContent(element)) {
          return true;
        }
      }
    }

    // Check if it's covering a significant portion of the screen
    const viewportArea = window.innerWidth * window.innerHeight;
    const elementArea = rect.width * rect.height;
    const coverageRatio = elementArea / viewportArea;

    // If it covers more than 30% of screen and has high z-index, likely an overlay
    if (coverageRatio > 0.3 && zIndex > 100) {
      // But make sure it's not the main content
      if (!this.isManhwaContent(element)) {
        return true;
      }
    }

    // Check for common ad dimensions even in overlays
    const commonAdSizes = [
      {w: 300, h: 250}, {w: 728, h: 90}, {w: 160, h: 600},
      {w: 320, h: 50}, {w: 970, h: 250}, {w: 336, h: 280}
    ];

    const isAdSize = commonAdSizes.some(size =>
      Math.abs(rect.width - size.w) < 10 && Math.abs(rect.height - size.h) < 10
    );

    if (isAdSize) {
      return true;
    }

    return false;
  }

  // Check if element contains manhwa content
  isManhwaContent(element) {
    // Check for images that look like manhwa pages
    const images = element.querySelectorAll('img');
    for (const img of images) {
      if (img.src && (
        img.src.includes('storage/media') ||
        img.src.includes('chapter') ||
        img.src.includes('page') ||
        img.src.includes('manhwa') ||
        img.src.includes('manga')
      )) {
        return true;
      }
    }

    // Check for manhwa-related text content
    const textContent = element.textContent?.toLowerCase() || '';
    const manhwaKeywords = ['chapter', 'prev', 'next', 'series', 'manga', 'manhwa'];

    if (manhwaKeywords.some(keyword => textContent.includes(keyword)) && textContent.length < 100) {
      return true;
    }

    return false;
  }

  // Setup redirect and pop-under blocking
  setupRedirectBlocking() {
    // Block common ad network domains
    const adDomains = [
      'pubrev.io', 'propellerads.com', 'popcash.net', 'popads.net',
      'adnium.com', 'exoclick.com', 'trafficjunky.com', 'juicyads.com',
      'adsterra.com', 'hilltopads.net', 'clickadu.com', 'bidvertiser.com'
    ];

    // Override window.open to block pop-unders
    const originalWindowOpen = window.open;
    window.open = function(url, name, features) {
      if (url && adDomains.some(domain => url.includes(domain))) {
        console.log('Blocked pop-under to:', url);
        return null;
      }
      return originalWindowOpen.call(window, url, name, features);
    };

    // Block redirect attempts
    const originalLocationAssign = window.location.assign;
    window.location.assign = function(url) {
      if (url && adDomains.some(domain => url.includes(domain))) {
        console.log('Blocked redirect to:', url);
        return;
      }
      return originalLocationAssign.call(window.location, url);
    };

    // Block clicks on ad links
    document.addEventListener('click', (event) => {
      const target = event.target.closest('a');
      if (target && target.href) {
        if (adDomains.some(domain => target.href.includes(domain))) {
          console.log('Blocked click on ad link:', target.href);
          event.preventDefault();
          event.stopPropagation();
          return false;
        }
      }
    }, true);

    // Block programmatic redirects
    let lastLocationHref = window.location.href;
    setInterval(() => {
      if (window.location.href !== lastLocationHref) {
        if (adDomains.some(domain => window.location.href.includes(domain))) {
          console.log('Blocked programmatic redirect to:', window.location.href);
          window.history.back();
        }
        lastLocationHref = window.location.href;
      }
    }, 100);
  }

  // Specifically target and remove Tailwind overlay patterns
  removeTailwindOverlays() {
    let removedCount = 0;

    // Target the exact problematic class pattern
    const specificSelectors = [
      'div[class*="fixed inset-0"][class*="bg-gray-900"][class*="bg-opacity"]',
      'div[class*="fixed inset-0"][class*="bg-black"][class*="bg-opacity"]',
      'div[class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 p-4 overflow-y-auto"]',
      '.fixed.inset-0.bg-gray-900',
      '.fixed.inset-0.bg-black',
      'div.fixed.inset-0[class*="bg-opacity"]'
    ];

    specificSelectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          if (!element.classList.contains('element-hider-hidden')) {
            // Double-check it's not manhwa content
            if (!this.isManhwaContent(element)) {
              console.log('Removing Tailwind overlay:', element.className);
              element.style.display = 'none';
              element.classList.add('element-hider-hidden');
              this.hiddenElements.add(element);

              // Also save as persistent selector
              const selector = this.generateSelector(element);
              if (selector && !this.isGenericSelector(selector)) {
                this.persistentSelectors.add(selector);
              }

              removedCount++;
            }
          }
        });
      } catch (error) {
        console.warn('Error with Tailwind selector:', selector, error);
      }
    });

    if (removedCount > 0) {
      this.saveSettings();
      this.showToast(`🎯 Removed ${removedCount} Tailwind overlays`);
      console.log(`Removed ${removedCount} Tailwind overlay elements`);
    } else {
      this.showToast('✅ No Tailwind overlays found');
    }
  }

  // Scan for ads outside manhwa content area
  scanForAdsOutsideManhwa() {
    let removedCount = 0;

    // Find the main manhwa content container
    const manhwaContainer = this.findManhwaContentContainer();

    // Scan for various ad patterns
    const adPatterns = [
      // iframes (most ads are in iframes)
      'iframe:not([src*="youtube"]):not([src*="vimeo"])',

      // Elements with ad-like dimensions
      'div[style*="width: 300px"][style*="height: 250px"]',
      'div[style*="width: 728px"][style*="height: 90px"]',
      'div[style*="width: 160px"][style*="height: 600px"]',
      'div[style*="width: 320px"][style*="height: 50px"]',
      'div[style*="width: 970px"][style*="height: 250px"]',
      'div[style*="width: 336px"][style*="height: 280px"]',

      // Google Ads
      '.adsbygoogle',
      'ins.adsbygoogle',
      '[data-ad-client]',

      // Ad network containers
      '[class*="monetix"]',
      '[id*="monetix"]',
      '[class*="propeller"]',
      '[id*="propeller"]',
      '[class*="exoclick"]',
      '[id*="exoclick"]',
      '[class*="adsterra"]',
      '[id*="adsterra"]',

      // Generic ad containers
      '[id*="banner"]',
      '[class*="banner"]:not([class*="cookie"])',
      '[id*="ad-"]',
      '[class*="ad-"]:not([class*="read"]):not([class*="load"])',
      '[id*="_ad_"]',
      '[class*="_ad_"]',

      // Promotional elements
      '[class*="promo"]',
      '[class*="promotion"]',
      '[id*="promo"]',
      '[id*="promotion"]',

      // Newsletter/subscription boxes
      '[class*="newsletter"]',
      '[class*="subscribe"]',
      '[class*="signup"]',
      '[id*="newsletter"]',
      '[id*="subscribe"]',
      '[id*="signup"]'
    ];

    adPatterns.forEach(pattern => {
      try {
        const elements = document.querySelectorAll(pattern);
        elements.forEach(element => {
          if (this.isAdOutsideManhwa(element, manhwaContainer)) {
            console.log('Removing ad outside manhwa content:', pattern, element);
            element.style.display = 'none';
            element.classList.add('element-hider-hidden');
            this.hiddenElements.add(element);

            // Save as persistent selector
            const selector = this.generateSelector(element);
            if (selector && !this.isGenericSelector(selector)) {
              this.persistentSelectors.add(selector);
            }

            removedCount++;
          }
        });
      } catch (error) {
        console.warn('Error scanning ad pattern:', pattern, error);
      }
    });

    if (removedCount > 0) {
      this.saveSettings();
      console.log(`Removed ${removedCount} ads outside manhwa content`);
    }
  }

  // Find the main manhwa content container
  findManhwaContentContainer() {
    // Look for common manhwa content containers
    const selectors = [
      // Common manhwa reader containers
      '[class*="reader"]',
      '[id*="reader"]',
      '[class*="chapter"]',
      '[id*="chapter"]',
      '[class*="content"]',
      '[id*="content"]',
      '[class*="manga"]',
      '[id*="manga"]',
      '[class*="manhwa"]',
      '[id*="manhwa"]',

      // Look for containers with multiple images
      'div:has(img[src*="storage/media"])',
      'div:has(img[src*="chapter"])',
      'div:has(img[src*="page"])'
    ];

    for (const selector of selectors) {
      try {
        const container = document.querySelector(selector);
        if (container && this.containsManhwaImages(container)) {
          console.log('Found manhwa container:', selector);
          return container;
        }
      } catch (error) {
        // Ignore selector errors
      }
    }

    // Fallback: find the container with the most images
    const allDivs = document.querySelectorAll('div');
    let bestContainer = null;
    let maxImages = 0;

    allDivs.forEach(div => {
      const images = div.querySelectorAll('img');
      if (images.length > maxImages && this.containsManhwaImages(div)) {
        maxImages = images.length;
        bestContainer = div;
      }
    });

    return bestContainer;
  }

  // Check if container has manhwa images
  containsManhwaImages(container) {
    const images = container.querySelectorAll('img');
    let manhwaImageCount = 0;

    images.forEach(img => {
      if (img.src && (
        img.src.includes('storage/media') ||
        img.src.includes('chapter') ||
        img.src.includes('page') ||
        img.src.includes('manhwa') ||
        img.src.includes('manga') ||
        img.src.includes('comic')
      )) {
        manhwaImageCount++;
      }
    });

    return manhwaImageCount >= 3; // At least 3 manhwa images
  }

  // Check if element is an ad outside manhwa content
  isAdOutsideManhwa(element, manhwaContainer) {
    // Don't hide if already hidden
    if (element.classList.contains('element-hider-hidden')) {
      return false;
    }

    // Don't hide if it's inside the manhwa container
    if (manhwaContainer && manhwaContainer.contains(element)) {
      return false;
    }

    // Don't hide if it contains manhwa content
    if (this.isManhwaContent(element)) {
      return false;
    }

    // Don't hide navigation or essential UI
    if (this.isEssentialUI(element)) {
      return false;
    }

    // Check if it looks like an ad
    const rect = element.getBoundingClientRect();
    const classAndId = (element.className + ' ' + element.id).toLowerCase();

    // Check for ad keywords
    const hasAdKeywords = /\b(ad|ads|banner|popup|promo|newsletter|subscribe|monetix|pubrev|propeller|exoclick|adsterra)\b/i;
    if (hasAdKeywords.test(classAndId)) {
      return true;
    }

    // Check for ad dimensions
    const commonAdSizes = [
      {w: 300, h: 250}, {w: 728, h: 90}, {w: 160, h: 600},
      {w: 320, h: 50}, {w: 970, h: 250}, {w: 336, h: 280}
    ];

    const isAdSize = commonAdSizes.some(size =>
      Math.abs(rect.width - size.w) < 10 && Math.abs(rect.height - size.h) < 10
    );

    if (isAdSize) {
      return true;
    }

    // Check if it's an iframe (likely ad)
    if (element.tagName === 'IFRAME' &&
        !element.src.includes('youtube') &&
        !element.src.includes('vimeo') &&
        !element.src.includes('disqus')) {
      return true;
    }

    return false;
  }

  // Check if element is essential UI that shouldn't be hidden
  isEssentialUI(element) {
    const classAndId = (element.className + ' ' + element.id).toLowerCase();

    // Navigation elements
    if (element.tagName === 'NAV' ||
        element.closest('nav') ||
        classAndId.includes('nav') ||
        classAndId.includes('menu')) {
      return true;
    }

    // Headers and footers (unless clearly ads)
    if ((element.tagName === 'HEADER' || element.tagName === 'FOOTER') &&
        !classAndId.includes('ad')) {
      return true;
    }

    // Forms
    if (element.tagName === 'FORM' || element.closest('form')) {
      return true;
    }

    // Manhwa-specific UI
    const manhwaKeywords = /\b(chapter|prev|next|series|read|bookmark|comment)\b/i;
    if (manhwaKeywords.test(classAndId)) {
      return true;
    }

    return false;
  }
}

// Initialize the element hider
const elementHider = new ElementHider();











