document.addEventListener('DOMContentLoaded', function() {
  const hideBtn = document.getElementById('hideBtn');
  const doneBtn = document.getElementById('doneBtn');
  const restoreBtn = document.getElementById('restoreBtn');
  const status = document.getElementById('status');
  
  // Check current mode
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    chrome.tabs.sendMessage(tabs[0].id, {action: 'getMode'}, function(response) {
      if (chrome.runtime.lastError) {
        console.log('Content script not ready:', chrome.runtime.lastError);
        return;
      }
      if (response && response.mode === 'hiding') {
        hideBtn.style.display = 'none';
        doneBtn.style.display = 'block';
        doneBtn.classList.add('active');
        status.textContent = 'Hover over elements to hide them';
      }
    });
  });
  
  hideBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      // First try to send message
      chrome.tabs.sendMessage(tabs[0].id, {action: 'startHiding'}, function(response) {
        if (chrome.runtime.lastError) {
          console.log('Content script not ready, injecting...');
          // If content script not ready, inject it
          chrome.scripting.executeScript({
            target: { tabId: tabs[0].id },
            files: ['content.js']
          }, function() {
            if (chrome.runtime.lastError) {
              status.textContent = 'Error: Please refresh the page';
              return;
            }
            // Try again after injection
            setTimeout(() => {
              chrome.tabs.sendMessage(tabs[0].id, {action: 'startHiding'}, function(response) {
                if (chrome.runtime.lastError) {
                  status.textContent = 'Please refresh the page and try again';
                  return;
                }
                hideBtn.style.display = 'none';
                doneBtn.style.display = 'block';
                doneBtn.classList.add('active');
                status.textContent = 'Hover over elements to hide them';
              });
            }, 100);
          });
        } else {
          hideBtn.style.display = 'none';
          doneBtn.style.display = 'block';
          doneBtn.classList.add('active');
          status.textContent = 'Hover over elements to hide them';
        }
      });
    });
  });
  
  doneBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: 'stopHiding'});
      hideBtn.style.display = 'block';
      doneBtn.style.display = 'none';
      doneBtn.classList.remove('active');
      status.textContent = 'Click to start hiding elements';
    });
  });
  
  restoreBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: 'restoreAll'});
      status.textContent = 'All elements restored!';
      setTimeout(() => {
        status.textContent = 'Click to start hiding elements';
      }, 2000);
    });
  });
});