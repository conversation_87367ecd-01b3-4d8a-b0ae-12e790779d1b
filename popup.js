document.addEventListener('DOMContentLoaded', function() {
  const hideBtn = document.getElementById('hideBtn');
  const doneBtn = document.getElementById('doneBtn');
  const restoreBtn = document.getElementById('restoreBtn');
  const status = document.getElementById('status');

  // New feature elements
  const autoCleanupBtn = document.getElementById('autoCleanupBtn');
  const selectorModeBtn = document.getElementById('selectorModeBtn');
  const selectorSection = document.getElementById('selectorSection');
  const selectorInput = document.getElementById('selectorInput');
  const hideBySelectorBtn = document.getElementById('hideBySelectorBtn');
  const exportBtn = document.getElementById('exportBtn');
  const importBtn = document.getElementById('importBtn');
  const importFile = document.getElementById('importFile');
  const clearGenericBtn = document.getElementById('clearGenericBtn');
  
  // Check current mode
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    chrome.tabs.sendMessage(tabs[0].id, {action: 'getMode'}, function(response) {
      if (chrome.runtime.lastError) {
        console.log('Content script not ready:', chrome.runtime.lastError);
        return;
      }
      if (response && response.mode === 'hiding') {
        hideBtn.style.display = 'none';
        doneBtn.style.display = 'block';
        doneBtn.classList.add('active');
        status.textContent = 'Hover over elements to hide them';
      }
    });
  });
  
  hideBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      // First try to send message
      chrome.tabs.sendMessage(tabs[0].id, {action: 'startHiding'}, function(response) {
        if (chrome.runtime.lastError) {
          console.log('Content script not ready, injecting...');
          // If content script not ready, inject it
          chrome.scripting.executeScript({
            target: { tabId: tabs[0].id },
            files: ['content.js']
          }, function() {
            if (chrome.runtime.lastError) {
              status.textContent = 'Error: Please refresh the page';
              return;
            }
            // Try again after injection
            setTimeout(() => {
              chrome.tabs.sendMessage(tabs[0].id, {action: 'startHiding'}, function(response) {
                if (chrome.runtime.lastError) {
                  status.textContent = 'Please refresh the page and try again';
                  return;
                }
                hideBtn.style.display = 'none';
                doneBtn.style.display = 'block';
                doneBtn.classList.add('active');
                status.textContent = 'Hover over elements to hide them';
              });
            }, 100);
          });
        } else {
          hideBtn.style.display = 'none';
          doneBtn.style.display = 'block';
          doneBtn.classList.add('active');
          status.textContent = 'Hover over elements to hide them';
        }
      });
    });
  });
  
  doneBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: 'stopHiding'});
      hideBtn.style.display = 'block';
      doneBtn.style.display = 'none';
      doneBtn.classList.remove('active');
      status.textContent = 'Click to start hiding elements';
    });
  });
  
  restoreBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: 'restoreAll'});
      status.textContent = 'All elements restored!';
      setTimeout(() => {
        status.textContent = 'Click to start hiding elements';
      }, 2000);
    });
  });

  // Auto-cleanup toggle
  autoCleanupBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: 'toggleAutoCleanup'}, function(response) {
        if (response && response.success) {
          autoCleanupBtn.classList.toggle('active', response.enabled);
          status.textContent = response.enabled ? 'Auto-cleanup enabled' : 'Auto-cleanup disabled';
          setTimeout(() => {
            status.textContent = 'Click to start hiding elements';
          }, 2000);
        }
      });
    });
  });

  // Selector mode toggle
  selectorModeBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: 'toggleSelectorMode'}, function(response) {
        if (response && response.success) {
          selectorModeBtn.classList.toggle('active', response.enabled);
          selectorSection.style.display = response.enabled ? 'block' : 'none';
          status.textContent = response.enabled ? 'Selector mode enabled' : 'Selector mode disabled';
          setTimeout(() => {
            status.textContent = 'Click to start hiding elements';
          }, 2000);
        }
      });
    });
  });

  // Hide by selector
  hideBySelectorBtn.addEventListener('click', function() {
    const selector = selectorInput.value.trim();
    if (!selector) {
      status.textContent = 'Please enter a CSS selector';
      return;
    }

    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: 'hideBySelector', selector: selector}, function(response) {
        if (response && response.success) {
          selectorInput.value = '';
          status.textContent = 'Selector applied successfully';
          setTimeout(() => {
            status.textContent = 'Click to start hiding elements';
          }, 2000);
        }
      });
    });
  });

  // Export settings
  exportBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: 'exportSettings'}, function(response) {
        if (response && response.success) {
          const dataStr = JSON.stringify(response.data, null, 2);
          const dataBlob = new Blob([dataStr], {type: 'application/json'});
          const url = URL.createObjectURL(dataBlob);

          const link = document.createElement('a');
          link.href = url;
          link.download = 'element-hider-settings.json';
          link.click();

          URL.revokeObjectURL(url);
          status.textContent = 'Settings exported successfully';
          setTimeout(() => {
            status.textContent = 'Click to start hiding elements';
          }, 2000);
        }
      });
    });
  });

  // Import settings
  importBtn.addEventListener('click', function() {
    importFile.click();
  });

  importFile.addEventListener('change', function(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
      try {
        const data = JSON.parse(e.target.result);
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
          chrome.tabs.sendMessage(tabs[0].id, {action: 'importSettings', data: data}, function(response) {
            if (response && response.success) {
              status.textContent = 'Settings imported successfully';
            } else {
              status.textContent = 'Failed to import settings';
            }
            setTimeout(() => {
              status.textContent = 'Click to start hiding elements';
            }, 2000);
          });
        });
      } catch (error) {
        status.textContent = 'Invalid settings file';
        setTimeout(() => {
          status.textContent = 'Click to start hiding elements';
        }, 2000);
      }
    };
    reader.readAsText(file);
  });

  // Clear generic selectors
  clearGenericBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: 'clearGenericSelectors'}, function(response) {
        if (response && response.success) {
          status.textContent = 'Generic selectors cleared';
          setTimeout(() => {
            status.textContent = 'Click to start hiding elements';
          }, 2000);
        }
      });
    });
  });

  // Allow Enter key in selector input
  selectorInput.addEventListener('keypress', function(event) {
    if (event.key === 'Enter') {
      hideBySelectorBtn.click();
    }
  });
});