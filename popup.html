<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 200px;
      padding: 15px;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 15px;
      text-align: center;
    }
    
    .btn {
      width: 100%;
      padding: 12px;
      margin: 8px 0;
      border: none;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }
    
    .btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }
    
    .btn:active {
      transform: translateY(0);
    }
    
    .btn.active {
      background: rgba(255, 255, 255, 0.9);
      color: #667eea;
    }
    
    .status {
      font-size: 12px;
      text-align: center;
      margin-top: 10px;
      opacity: 0.8;
    }
  </style>
</head>
<body>
  <div class="title">Element Hider</div>
  
  <button id="hideBtn" class="btn">🎯 Hide Elements</button>
  <button id="doneBtn" class="btn" style="display: none;">✅ Done Hiding</button>
  <button id="restoreBtn" class="btn">🔄 Restore All</button>
  
  <div id="status" class="status">Click to start hiding elements</div>
  
  <script src="popup.js"></script>
</body>
</html>