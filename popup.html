<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 280px;
      padding: 15px;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 15px;
      text-align: center;
    }
    
    .btn {
      width: 100%;
      padding: 12px;
      margin: 8px 0;
      border: none;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }
    
    .btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }
    
    .btn:active {
      transform: translateY(0);
    }
    
    .btn.active {
      background: rgba(255, 255, 255, 0.9);
      color: #667eea;
    }
    
    .status {
      font-size: 12px;
      text-align: center;
      margin-top: 10px;
      opacity: 0.8;
    }

    .section {
      margin: 15px 0;
      padding-top: 10px;
      border-top: 1px solid rgba(255, 255, 255, 0.2);
    }

    .section-title {
      font-size: 13px;
      font-weight: 600;
      margin-bottom: 8px;
      opacity: 0.9;
    }

    .btn-small {
      padding: 8px;
      font-size: 12px;
      margin: 4px 0;
    }

    .input-group {
      margin: 8px 0;
    }

    .input-group input {
      width: 100%;
      padding: 8px;
      border: none;
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      font-size: 12px;
      box-sizing: border-box;
    }

    .input-group input::placeholder {
      color: rgba(255, 255, 255, 0.7);
    }

    .toggle-btn {
      background: rgba(255, 255, 255, 0.1);
    }

    .toggle-btn.active {
      background: rgba(76, 175, 80, 0.8);
    }
  </style>
</head>
<body>
  <div class="title">Element Hider Pro</div>

  <button id="hideBtn" class="btn">🎯 Hide Elements</button>
  <button id="doneBtn" class="btn" style="display: none;">✅ Done Hiding</button>
  <button id="restoreBtn" class="btn">🔄 Restore All</button>

  <div class="section">
    <div class="section-title">Advanced Features</div>
    <button id="autoCleanupBtn" class="btn btn-small toggle-btn">🧼 Auto-Cleanup</button>
    <button id="selectorModeBtn" class="btn btn-small toggle-btn">🎯 Selector Mode</button>
  </div>

  <div class="section" id="selectorSection" style="display: none;">
    <div class="section-title">Hide by CSS Selector</div>
    <div class="input-group">
      <input type="text" id="selectorInput" placeholder="e.g., .ad, #popup, [class*='banner']">
    </div>
    <button id="hideBySelectorBtn" class="btn btn-small">Hide Elements</button>
  </div>

  <div class="section">
    <div class="section-title">Settings</div>
    <button id="exportBtn" class="btn btn-small">📤 Export Settings</button>
    <button id="importBtn" class="btn btn-small">📥 Import Settings</button>
    <button id="clearGenericBtn" class="btn btn-small">🧹 Fix Generic Selectors</button>
    <input type="file" id="importFile" accept=".json" style="display: none;">
  </div>

  <div id="status" class="status">Click to start hiding elements</div>
  
  <script src="popup.js"></script>
</body>
</html>