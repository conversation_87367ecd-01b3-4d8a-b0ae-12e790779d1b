# Chrome iOS Hide Elements Extension

A powerful Chrome extension that allows you to hide unwanted elements on any webpage with a simple click. Perfect for removing ads, distracting content, or creating a cleaner browsing experience.

## 🌟 Features

- **One-Click Hiding**: Simply hover over any element and click the red highlighted area to hide it instantly
- **Visual Feedback**: Elements are highlighted with a red border when you hover over them
- **Smooth Animations**: Beautiful particle effects and animations when hiding elements
- **Restore Functionality**: Easily restore all hidden elements with one click
- **Non-Destructive**: Elements are only hidden, not deleted - they can always be restored
- **iOS-Style Design**: Clean, modern interface inspired by iOS design principles

## 🚀 How to Use

### Installation
1. Download or clone this repository
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension folder
5. The extension icon will appear in your Chrome toolbar

### Using the Extension

1. **Start Hiding Mode**
   - Click the extension icon in your Chrome toolbar
   - Click the "Start Hiding" button
   - You'll see a toast notification: "🎯 Hover over elements, then click the red area to hide them"

2. **Hide Elements**
   - Hover over any element on the webpage
   - The element will be highlighted with a **red border and background**
   - Click anywhere on the red highlighted area to hide the element
   - Alternatively, click the blue "Hide" button that appears
   - Watch the satisfying animation as the element disappears!

3. **Stop Hiding Mode**
   - Click the "Done" button in the floating toolbar at the bottom of the page
   - Or click the extension icon and select "Done"

4. **Restore Hidden Elements**
   - Click the extension icon
   - Click "Restore All" to bring back all hidden elements

## 🎯 Key Interactions

- **Red Highlighted Area**: Click anywhere on the red highlighted area to hide an element
- **Hide Button**: Alternative way to hide elements using the blue "Hide" button
- **Hover Effects**: Elements scale slightly and glow when hovered for better visual feedback
- **Animations**: Particle effects and ripple animations provide satisfying visual feedback

## 🛠️ Technical Details

### Files Structure
```
├── manifest.json       # Extension configuration
├── popup.html         # Extension popup interface
├── popup.js           # Popup functionality
├── popup.css          # Popup styling
├── content.js         # Main content script
├── content.css        # Element highlighting and animation styles
└── README.md          # This file
```

### Key Features Implementation
- **Event Handling**: Uses both capture and non-capture event listeners for reliable click detection
- **Visual Highlighting**: CSS-based red highlighting with smooth transitions
- **Animation System**: Custom particle effects and ripple animations
- **State Management**: Tracks hidden elements and original display values for restoration
- **Cross-Site Compatibility**: Works on any website without conflicts

## 🎨 Customization

The extension uses CSS custom properties and can be easily customized:

- **Highlight Color**: Modify the red highlight color in `content.css`
- **Animation Duration**: Adjust timing in the animation keyframes
- **Particle Effects**: Customize particle colors and behavior in `content.js`
- **UI Elements**: Modify button styles and positioning

## 🔧 Development

### Prerequisites
- Google Chrome browser
- Basic knowledge of HTML, CSS, and JavaScript

### Local Development
1. Make changes to the source files
2. Go to `chrome://extensions/`
3. Click the refresh icon on your extension card
4. Test your changes on any webpage

### Debugging
- Open Chrome DevTools (F12)
- Check the Console tab for debug messages
- The extension logs detailed information about element interactions

## 📱 iOS-Inspired Design

This extension follows iOS design principles:
- **Clean Interface**: Minimal, uncluttered UI
- **Smooth Animations**: 60fps animations with easing curves
- **Intuitive Interactions**: Familiar gestures and visual feedback
- **Modern Typography**: System fonts for consistency
- **Rounded Corners**: Consistent border radius throughout
- **Backdrop Blur**: Modern glass-morphism effects

## � Security & Permissions

- The extension does not collect or transmit any user data
- It only interacts with the DOM of pages you're actively viewing
- No third-party APIs or tracking scripts are used
- All data processing happens locally in your browser
- No network requests are made by the extension
- Your browsing privacy is fully protected

## �🚫 Limitations

- Hidden elements are restored when the page is refreshed
- Some websites with complex JavaScript may interfere with element detection
- Elements with `pointer-events: none` cannot be selected
- The extension respects website security policies

## 🤝 Contributing

Feel free to contribute to this project:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🆘 Support

If you encounter any issues:
1. Check the browser console for error messages
2. Try refreshing the page and restarting the extension
3. Ensure you're using the latest version of Chrome
4. Some websites may have security restrictions that prevent the extension from working

---

**Enjoy a cleaner, more focused browsing experience! 🎉**
