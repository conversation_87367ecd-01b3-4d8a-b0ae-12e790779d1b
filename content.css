/* Element Hider Styles */
.element-hider-highlight {
  outline: 3px solid #007AFF !important;
  outline-offset: 2px !important;
  background: rgba(0, 122, 255, 0.1) !important;
  position: relative !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
}

.element-hider-highlight:hover {
  outline-color: #FF3B30 !important;
  background: rgba(255, 59, 48, 0.1) !important;
}

.element-hider-delete-btn {
  position: fixed !important;
  transform: translate(-50%, -50%) !important;
  background: #0064e1 !important; /* Safari blue color */
  color: white !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  padding: 6px 12px !important;
  border-radius: 20px !important; /* More rounded corners */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  z-index: 10000 !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;
  transition: all 0.3s ease !important;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif !important;
  line-height: 1 !important;
  backdrop-filter: blur(5px) !important;
  border: none !important;
  pointer-events: auto !important;
}

.element-hider-delete-btn:hover {
  background: #0056cc !important; /* Slightly darker blue on hover */
  transform: translate(-50%, -50%) scale(1.05) !important;
}

.element-hider-delete-btn:active {
  transform: translate(-50%, -50%) scale(0.95) !important;
}

.element-hider-hidden {
  opacity: 0 !important;
  pointer-events: none !important;
  transition: opacity 0.5s ease !important;
}

.element-hider-particle {
  position: absolute !important;
  width: 6px !important;
  height: 6px !important;
  background: #007AFF !important;
  border-radius: 50% !important;
  pointer-events: none !important;
  z-index: 10000 !important;
  animation: elementHiderParticle 1s ease-out forwards !important;
}

@keyframes elementHiderParticle {
  0% {
    opacity: 1;
    transform: scale(1) translate(0, 0);
  }
  100% {
    opacity: 0;
    transform: scale(0) translate(var(--particle-x), var(--particle-y));
  }
}

.element-hider-ripple {
  position: absolute !important;
  border: 2px solid #007AFF !important;
  border-radius: 50% !important;
  pointer-events: none !important;
  z-index: 10000 !important;
  animation: elementHiderRipple 0.6s ease-out forwards !important;
}

@keyframes elementHiderRipple {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 200px;
    height: 200px;
    opacity: 0;
  }
}

.element-hider-toast {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  background: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  padding: 12px 20px !important;
  border-radius: 25px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
  z-index: 10001 !important;
  backdrop-filter: blur(10px) !important;
  animation: elementHiderToastSlide 0.3s ease-out !important;
}

@keyframes elementHiderToastSlide {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.element-hider-toolbar {
  position: fixed !important;
  bottom: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: rgba(0, 0, 0, 0.9) !important;
  padding: 12px 20px !important;
  border-radius: 25px !important;
  color: white !important;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif !important;
  font-size: 14px !important;
  z-index: 10001 !important;
  backdrop-filter: blur(10px) !important;
  display: flex !important;
  align-items: center !important;
  gap: 15px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
}

.element-hider-toolbar button {
  background: #007AFF !important;
  border: none !important;
  color: white !important;
  padding: 8px 16px !important;
  border-radius: 15px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.element-hider-toolbar button:hover {
  background: #0056CC !important;
  transform: translateY(-1px) !important;
}



